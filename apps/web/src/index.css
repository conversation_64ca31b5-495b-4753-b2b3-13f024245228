@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light theme with subtle tints (for when dark theme is not active) */
    --background: oklch(0.97 0.015 260);
    --foreground: oklch(0.25 0.02 260);
    --card: oklch(0.98 0.01 260);
    --card-foreground: oklch(0.25 0.02 260);
    --popover: oklch(0.98 0.01 260);
    --popover-foreground: oklch(0.25 0.02 260);
    --primary: oklch(0.62 0.19 185);
    --primary-foreground: oklch(0.98 0 0);
    --secondary: oklch(0.94 0.015 260);
    --secondary-foreground: oklch(0.45 0.03 257.68);
    --muted: oklch(0.93 0.02 260);
    --muted-foreground: oklch(0.55 0.02 264.41);
    --accent: oklch(0.93 0.03 233.56);
    --accent-foreground: oklch(0.38 0.14 265.59);
    --destructive: oklch(0.64 0.21 25.39);
    --destructive-foreground: oklch(0.98 0 0);
    --success: oklch(0.62 0.19 142.50);
    --success-foreground: oklch(0.98 0 0);
    --warning: oklch(0.75 0.15 82.50);
    --warning-foreground: oklch(0.20 0 0);
    --info: oklch(0.62 0.19 196);
    --info-foreground: oklch(0.98 0 0);
    --border: oklch(0.90 0.02 261.82);
    --input: oklch(0.90 0.02 261.82);
    --ring: oklch(0.62 0.19 185);
    --ring-50: oklch(0.62 0.19 185 / 0.5);
    --chart-1: oklch(0.62 0.19 185);
    --chart-2: oklch(0.55 0.22 196);
    --chart-3: oklch(0.49 0.22 205);
    --chart-4: oklch(0.42 0.18 215);
    --chart-5: oklch(0.38 0.14 225);
    --sidebar: oklch(0.95 0.015 260);
    --sidebar-foreground: oklch(0.14 0 0);
    --sidebar-primary: oklch(0.20 0 0);
    --sidebar-primary-foreground: oklch(0.98 0 0);
    --sidebar-accent: oklch(0.93 0.015 260);
    --sidebar-accent-foreground: oklch(0.20 0 0);
    --sidebar-border: oklch(0.90 0.01 260);
    --sidebar-ring: oklch(0.71 0 0);

    /* Animation Durations */
    --duration-fast: 0.2s;
    --duration-normal: 0.5s;
    --duration-slow: 0.8s;

    /* Animation Easings */
    --ease-default: cubic-bezier(0.4, 0, 0.2, 1);
    --ease-in: cubic-bezier(0.4, 0, 1, 1);
    --ease-out: cubic-bezier(0, 0, 0.2, 1);
    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
    --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);

    --font-sans: 'Geist', 'Geist Fallback', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
    --font-serif: Source Serif 4, serif;
    --font-mono: JetBrains Mono, monospace;

    --radius: 0.375rem;

    --shadow-2xs: 0 1px 3px 0px oklch(0.00 0 0 / 0.05);
    --shadow-xs: 0 1px 3px 0px oklch(0.00 0 0 / 0.05);
    --shadow-sm: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 1px 2px -1px oklch(0.00 0 0 / 0.10);
    --shadow: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 1px 2px -1px oklch(0.00 0 0 / 0.10);
    --shadow-md: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 2px 4px -1px oklch(0.00 0 0 / 0.10);
    --shadow-lg: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 4px 6px -1px oklch(0.00 0 0 / 0.10);
    --shadow-xl: 0 1px 3px 0px oklch(0.00 0 0 / 0.10), 0 8px 10px -1px oklch(0.00 0 0 / 0.10);
    --shadow-2xl: 0 1px 3px 0px oklch(0.00 0 0 / 0.25);
  }

  .dark {
    /* Dark theme extracted from hero section */
    --background: oklch(0.15 0.02 245);
    --foreground: oklch(0.95 0.015 260);
    --card: oklch(0.22 0.03 245);
    --card-foreground: oklch(0.95 0.015 260);
    --popover: oklch(0.22 0.03 245);
    --popover-foreground: oklch(0.95 0.015 260);
    --primary: oklch(0.65 0.19 185);
    --primary-foreground: oklch(0.99 0 0);
    --secondary: oklch(0.25 0.03 245);
    --secondary-foreground: oklch(0.92 0.02 260);
    --muted: oklch(0.25 0.03 245);
    --muted-foreground: oklch(0.75 0.02 260);
    --accent: oklch(0.38 0.14 225);
    --accent-foreground: oklch(0.92 0.06 255);
    --destructive: oklch(0.64 0.21 25.39);
    --destructive-foreground: oklch(0.99 0 0);
    --success: oklch(0.62 0.19 142.50);
    --success-foreground: oklch(0.99 0 0);
    --warning: oklch(0.75 0.15 82.50);
    --warning-foreground: oklch(0.20 0 0);
    --info: oklch(0.65 0.19 196);
    --info-foreground: oklch(0.99 0 0);
    --border: oklch(0.30 0.02 245);
    --input: oklch(0.30 0.02 245);
    --ring: oklch(0.65 0.19 185);
    --ring-50: oklch(0.65 0.19 185 / 0.5);
    --chart-1: oklch(0.71 0.15 185);
    --chart-2: oklch(0.65 0.19 196);
    --chart-3: oklch(0.60 0.20 205);
    --chart-4: oklch(0.55 0.19 215);
    --chart-5: oklch(0.50 0.17 225);
    --sidebar: oklch(0.20 0.03 245);
    --sidebar-foreground: oklch(0.95 0.015 260);
    --sidebar-primary: oklch(0.65 0.19 185);
    --sidebar-primary-foreground: oklch(0.99 0 0);
    --sidebar-accent: oklch(0.25 0.03 245);
    --sidebar-accent-foreground: oklch(0.95 0.015 260);
    --sidebar-border: oklch(0.30 0.02 245);
    --sidebar-ring: oklch(0.55 0.10 200);

    /* Enhanced shadows for depth in dark mode */
    --shadow-2xs: 0 1px 3px 0px oklch(0.00 0 0 / 0.2);
    --shadow-xs: 0 1px 3px 0px oklch(0.00 0 0 / 0.2);
    --shadow-sm: 0 1px 3px 0px oklch(0.00 0 0 / 0.25), 0 1px 2px -1px oklch(0.00 0 0 / 0.25);
    --shadow: 0 1px 3px 0px oklch(0.00 0 0 / 0.25), 0 1px 2px -1px oklch(0.00 0 0 / 0.25);
    --shadow-md: 0 2px 4px 0px oklch(0.00 0 0 / 0.3), 0 2px 5px -1px oklch(0.00 0 0 / 0.3);
    --shadow-lg: 0 3px 5px 0px oklch(0.00 0 0 / 0.3), 0 4px 6px -1px oklch(0.00 0 0 / 0.3);
    --shadow-xl: 0 4px 6px 0px oklch(0.00 0 0 / 0.35), 0 8px 10px -1px oklch(0.00 0 0 / 0.35);
    --shadow-2xl: 0 5px 8px 0px oklch(0.00 0 0 / 0.4);
  }
}

@layer base {
  * {
    @apply border-border outline-ring-50;
    box-sizing: border-box;
    /* Override Tailwind's default ring color to use our primary color */
    --tw-ring-color: var(--primary);
  }
  html, body {
    @apply bg-background text-foreground;
    margin: 0;
    padding: 0;
    height: 100%;
    width: 100%;
    overflow: hidden;
    position: fixed;
    inset: 0;
  }

  /* Remove default scrollbar styles and reserved space */
  * {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }

  *::-webkit-scrollbar {
    display: none;  /* Chrome, Safari, Opera */
    width: 0;
    height: 0;
  }
  
  #root {
    @apply flex flex-col;
    position: fixed;
    inset: 0;
    overflow: hidden;
  }

  /* Custom scrollbar styles only for elements that need them */
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(155, 155, 155, 0.5) transparent;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    display: block;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(155, 155, 155, 0.5);
    border-radius: 20px;
    border: 2px solid transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(155, 155, 155, 0.7);
  }
}

/* Add gradient text utility */
@layer utilities {
  .text-gradient-primary {
    background: linear-gradient(to right, oklch(0.65 0.19 170), oklch(0.60 0.20 220));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
  }

  .bg-gradient-primary {
    background: linear-gradient(120deg, oklch(0.65 0.19 170), oklch(0.60 0.20 220));
  }

  .bg-glow {
    position: relative;
  }

  .bg-glow::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 25% 25%, oklch(0.30 0.10 210 / 0.15), transparent 50%);
    pointer-events: none;
    z-index: -1;
  }

  /* Utility class to hide scrollbars but allow scrolling */
  .no-scrollbar {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }

  .no-scrollbar::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
    background: transparent;
  }

  /* Utility class for thin, styled scrollbars */
  .custom-scrollbar {
    scrollbar-width: thin;  /* Firefox */
    scrollbar-color: rgba(155, 155, 155, 0.5) transparent;  /* Firefox */
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(155, 155, 155, 0.5);
    border-radius: 20px;
    border: 2px solid transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(155, 155, 155, 0.7);
  }

  .custom-scrollbar::-webkit-scrollbar-corner {
    background: transparent;
  }
}

@keyframes accordion-down {
  from {
    height: 0;
  }
  to {
    height: var(--radix-accordion-content-height);
  }
}

@keyframes accordion-up {
  from {
    height: var(--radix-accordion-content-height);
  }
  to {
    height: 0;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    opacity: 0.05;
    transform: scale(1);
  }
  100% {
    opacity: 0.2;
    transform: scale(1.1);
  }
}

@keyframes float {
  0% {
    transform: translateY(0) rotate(0);
  }
  50% {
    transform: translateY(-20px) rotate(5deg);
  }
  100% {
    transform: translateY(0) rotate(0);
  }
}

@layer utilities {
  .animate-accordion-down {
    animation: accordion-down 0.2s ease-out;
  }
  .animate-accordion-up {
    animation: accordion-up 0.2s ease-out;
  }
  .animate-fadeIn {
    animation: fadeIn 0.5s ease-out forwards;
  }
  .animate-gradient-shift {
    animation: gradient-shift 8s ease infinite;
  }
  .animate-pulse-subtle {
    animation: pulse 2s ease-in-out infinite alternate;
  }
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
