import { AppointmentFilters } from "@/components/appointments/AppointmentFilters";
import { AppointmentStats } from "@/components/appointments/AppointmentStats";
import { AppointmentTable } from "@/components/appointments/AppointmentTable";
import { Button } from "@/components/ui/button";
import { DataPagination } from "@/components/ui/data-pagination";
import { useAppointments } from "@/hooks/dashboard/useAppointments";
import { useAuth } from "@/hooks/useAuth";
import { usePagination } from "@/hooks/usePagination";
import { useUserRoles } from "@/hooks/useUserRoles";
import { TABLE_STORAGE_KEYS } from "@/lib/table-preferences";
import { AppointmentFilters as AppointmentFiltersType } from "@/types/appointment";
import { Calendar, Plus } from "lucide-react";
import { useMemo, useState } from "react";
import { useNavigate } from "react-router-dom";

export function AppointmentsManagePage() {
  const { organization } = useAuth();
  const { isSystemAdmin } = useUserRoles();
  const navigate = useNavigate();

  const [filters, setFilters] = useState<AppointmentFiltersType>({
    search: "",
    status: "all",
    dateRange: undefined,
    providerId: undefined,
    departmentId: undefined,
  });

  // Get initial appointments count
  const { totalCount } = useAppointments({
    status: filters.status !== "all" ? filters.status as any : undefined,
    dateRange: filters.dateRange,
    limit: 10,
    offset: 0,
  });

  // Pagination setup with configurable page size and localStorage persistence
  const {
    currentPage,
    offset,
    pageSize,
    itemsPerPage,
    goToPage,
    setPageSize,
  } = usePagination({
    totalCount, // Pass the actual totalCount
    allowPageSizeChange: true,
    defaultPageSize: 10,
    storageKey: TABLE_STORAGE_KEYS.APPOINTMENTS, // Unique key for appointments table
  });

  // Memoize the options object to prevent unnecessary re-renders
  const appointmentOptions = useMemo(() => ({
    limit: itemsPerPage,
    offset,
    // Convert filters to useAppointments options
    status: filters.status !== "all" ? filters.status as any : undefined,
    dateRange: filters.dateRange,
  }), [itemsPerPage, offset, filters.status, filters.dateRange]);

  // Get paginated appointments
  const { 
    appointments: paginatedAppointments, 
    isLoading: isLoadingPaginated 
  } = useAppointments(appointmentOptions);

  // Calculate actual total pages
  const actualTotalPages = Math.ceil(totalCount / itemsPerPage);

  const handleFiltersChange = (newFilters: AppointmentFiltersType) => {
    setFilters(newFilters);
    goToPage(1); // Reset to first page when filters change
  };

  const handleCreateAppointment = () => {
    navigate("/appointments/new");
  };

  const getPageTitle = () => {
    if (isSystemAdmin && organization?.id === "system-admin-all-orgs") {
      return "All Appointments";
    }
    return `${organization?.name || "Organization"} Appointments`;
  };

  const getPageDescription = () => {
    if (isSystemAdmin && organization?.id === "system-admin-all-orgs") {
      return "Manage appointments across all organizations";
    }
    return "Manage and view appointment schedules for your organization";
  };

  return (
    <div className="space-y-8 w-full">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">{getPageTitle()}</h1>
          <p className="text-muted-foreground mt-1">{getPageDescription()}</p>
        </div>
        <Button onClick={handleCreateAppointment}>
          <Plus className="mr-2 h-4 w-4" />
          Schedule Appointment
        </Button>
      </div>

      {/* Stats */}
      <AppointmentStats 
        appointments={paginatedAppointments} 
        totalCount={totalCount} 
        isLoading={isLoadingPaginated} 
      />

      {/* Main Content */}
      <div className="bg-card rounded-lg border p-6 space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            <h2 className="text-xl font-semibold">Appointment Schedule</h2>
          </div>
          <div className="text-sm text-muted-foreground">
            {totalCount} appointment{totalCount !== 1 ? "s" : ""}
          </div>
        </div>

        {/* Filters */}
        <AppointmentFilters
          filters={filters}
          onFiltersChange={handleFiltersChange}
          totalCount={totalCount}
          organization={organization}
          isSystemAdmin={isSystemAdmin}
        />

        {/* Table */}
        <AppointmentTable
          appointments={paginatedAppointments}
          isLoading={isLoadingPaginated}
          organization={organization}
          isSystemAdmin={isSystemAdmin}
        />

        {/* Pagination */}
        <DataPagination
          currentPage={currentPage}
          totalPages={actualTotalPages}
          onPageChange={goToPage}
          pageSize={pageSize}
          onPageSizeChange={setPageSize}
          totalCount={totalCount}
          showPageSizeSelector={true}
        />
      </div>
    </div>
  );
}
